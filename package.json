{"name": "theinfini_ai_backend", "version": "1.0.0", "description": "", "type": "module", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "prod": "npm start", "test": "echo \"Error: no test specified\" && exit 1", "init-plans": "node scripts/init-subscription-plans.js", "init-llm-models": "node scripts/init-llm-models.js", "test-llm-models": "node scripts/test-llm-models.js", "test-subscription": "node scripts/test-subscription-system.js", "setup-llm-logos": "node scripts/setup-llm-logos.js", "update-logo-paths": "node scripts/update-logo-paths.js", "migrate-api-keys": "node scripts/migrate-api-keys.js", "test-drive-utility": "node scripts/test-drive-utility-app.js"}, "dependencies": {"@aws-sdk/client-s3": "^3.839.0", "@aws-sdk/s3-request-presigner": "^3.839.0", "@langchain/anthropic": "^0.3.21", "@langchain/core": "^0.3.57", "@langchain/google-genai": "^0.2.16", "@langchain/openai": "^0.5.11", "@langchain/tavily": "^0.1.4", "@pinecone-database/pinecone": "^6.0.1", "axios": "^1.11.0", "bcryptjs": "^3.0.2", "cheerio": "^1.1.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "googleapis": "^159.0.0", "handlebars": "^4.7.8", "helmet": "^8.1.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "langchain": "^0.3.27", "mammoth": "^1.9.1", "multer": "^2.0.1", "mysql2": "^3.14.1", "nodemailer": "^7.0.3", "openai": "^5.10.2", "pdf-lib": "^1.17.1", "pdf2pic": "^3.2.0", "puppeteer-core": "^21.3.3", "razorpay": "^2.9.6", "sequelize": "^6.37.7", "sharp": "^0.34.2", "uuid": "^11.1.0", "winston": "^3.17.0", "xlsx": "^0.18.5"}, "devDependencies": {"nodemon": "^3.1.10"}, "private": true}