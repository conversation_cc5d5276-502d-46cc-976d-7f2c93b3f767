import { BaseAgent } from './BaseAgent.js';
import { DriveUtilityApp } from '../services/DriveUtilityApp.js';
import { AIMessage } from '@langchain/core/messages';
import { logger } from '../utils/logger.js';

/**
 * Agent to get detailed information about a specific spreadsheet
 * Uses DriveUtilityApp.getAvailableSheets and getSheetData methods
 */
export class GetSpreadsheetDetailsAgent extends BaseAgent {
  constructor(llmModel = 'gpt-4o-mini') {
    super(
      'GetSpreadsheetDetailsAgent',
      'I help users get detailed information about a specific Google Sheets spreadsheet, including all sheets, their properties, and metadata.',
      llmModel
    );
  }

  /**
   * Initialize the agent's state graph
   */
  async initializeGraph() {
    this.graph = this.createBasicWorkflow();
  }

  /**
   * Process input parameters
   * @param {Object} state - Current state
   * @returns {Object} Updated state
   */
  async processInput(state) {
    try {
      state.messages.push(new AIMessage('Processing input for getting spreadsheet details...'));
      
      const input = state.input || {};
      
      // Validate required spreadsheet ID
      if (!input.spreadsheetId) {
        throw new Error('Spreadsheet ID is required');
      }
      
      if (typeof input.spreadsheetId !== 'string' || input.spreadsheetId.trim().length === 0) {
        throw new Error('Spreadsheet ID must be a non-empty string');
      }

      // Validate optional parameters
      const includeSheetData = input.includeSheetData !== false; // default true
      const includeMetadata = input.includeMetadata !== false; // default true
      const specificSheets = input.specificSheets || null; // array of sheet names
      
      if (specificSheets && !Array.isArray(specificSheets)) {
        throw new Error('specificSheets must be an array of sheet names');
      }

      state.processedInput = {
        spreadsheetId: input.spreadsheetId.trim(),
        includeSheetData,
        includeMetadata,
        specificSheets,
        maxDataRows: input.maxDataRows || 1000, // limit data rows for performance
        includeFormulas: input.includeFormulas || false
      };

      state.messages.push(new AIMessage('Input processed successfully'));
      return state;
    } catch (error) {
      state.error = error.message;
      state.messages.push(new AIMessage(`Input processing failed: ${error.message}`));
      throw error;
    }
  }

  /**
   * Execute the main operation - get spreadsheet details
   * @param {Object} state - Current state
   * @returns {Object} Updated state
   */
  async executeOperation(state) {
    try {
      const { spreadsheetId, includeSheetData, includeMetadata, specificSheets } = state.processedInput;
      
      state.messages.push(new AIMessage(`Fetching details for spreadsheet: ${spreadsheetId}`));
      
      // Get basic spreadsheet information and available sheets
      const sheetsInfo = await DriveUtilityApp.getAvailableSheets(state.userId, spreadsheetId);
      
      if (!sheetsInfo.success) {
        throw new Error('Failed to get spreadsheet information');
      }

      const result = {
        spreadsheetId: sheetsInfo.spreadsheetId,
        spreadsheetTitle: sheetsInfo.spreadsheetTitle,
        totalSheets: sheetsInfo.totalSheets,
        sheets: sheetsInfo.sheets,
        metadata: {}
      };

      // Get detailed data for each sheet if requested
      if (includeSheetData) {
        state.messages.push(new AIMessage('Fetching detailed sheet data...'));
        
        const sheetsToProcess = specificSheets 
          ? sheetsInfo.sheets.filter(sheet => specificSheets.includes(sheet.title))
          : sheetsInfo.sheets;

        const sheetDataPromises = sheetsToProcess.map(async (sheet) => {
          try {
            const sheetData = await DriveUtilityApp.getSheetData(
              state.userId, 
              spreadsheetId, 
              sheet.title
            );
            
            if (sheetData.success) {
              return {
                ...sheet,
                detailedData: sheetData.sheetData,
                hasData: sheetData.sheetData.actualDataDimensions.hasData,
                dataRowCount: sheetData.sheetData.actualDataDimensions.rowCount,
                dataColumnCount: sheetData.sheetData.actualDataDimensions.columnCount
              };
            } else {
              return {
                ...sheet,
                error: 'Failed to fetch sheet data',
                hasData: false
              };
            }
          } catch (error) {
            logger.warn(`Error fetching data for sheet ${sheet.title}:`, error.message);
            return {
              ...sheet,
              error: error.message,
              hasData: false
            };
          }
        });

        const detailedSheets = await Promise.all(sheetDataPromises);
        result.sheets = detailedSheets;
      }

      // Add metadata if requested
      if (includeMetadata) {
        result.metadata = {
          totalDataRows: result.sheets.reduce((sum, sheet) => 
            sum + (sheet.dataRowCount || 0), 0),
          totalDataColumns: Math.max(...result.sheets.map(sheet => 
            sheet.dataColumnCount || 0), 0),
          sheetsWithData: result.sheets.filter(sheet => sheet.hasData).length,
          sheetsWithErrors: result.sheets.filter(sheet => sheet.error).length,
          fetchedAt: new Date().toISOString()
        };
      }

      state.operationResult = result;
      state.messages.push(new AIMessage(`Successfully retrieved details for spreadsheet with ${result.totalSheets} sheets`));
      
      return state;
    } catch (error) {
      state.error = error.message;
      state.messages.push(new AIMessage(`Operation failed: ${error.message}`));
      throw error;
    }
  }

  /**
   * Format the output
   * @param {Object} state - Current state
   * @returns {Object} Updated state
   */
  async formatOutput(state) {
    try {
      if (state.error) {
        state.output = {
          success: false,
          error: state.error,
          timestamp: new Date().toISOString(),
          agentName: this.name
        };
      } else {
        const result = state.operationResult;
        state.output = {
          success: true,
          data: {
            spreadsheet: {
              id: result.spreadsheetId,
              title: result.spreadsheetTitle,
              totalSheets: result.totalSheets,
              sheets: result.sheets.map(sheet => ({
                sheetId: sheet.sheetId,
                title: sheet.title,
                index: sheet.index,
                sheetType: sheet.sheetType,
                hidden: sheet.hidden,
                gridProperties: sheet.gridProperties,
                hasData: sheet.hasData,
                dataRowCount: sheet.dataRowCount,
                dataColumnCount: sheet.dataColumnCount,
                detailedData: state.processedInput.includeSheetData ? sheet.detailedData : undefined,
                error: sheet.error
              })),
              metadata: result.metadata
            },
            query: {
              spreadsheetId: state.processedInput.spreadsheetId,
              includeSheetData: state.processedInput.includeSheetData,
              includeMetadata: state.processedInput.includeMetadata,
              specificSheets: state.processedInput.specificSheets
            }
          },
          timestamp: new Date().toISOString(),
          agentName: this.name
        };
      }

      state.messages.push(new AIMessage('Output formatted successfully'));
      return state;
    } catch (error) {
      state.error = error.message;
      state.messages.push(new AIMessage(`Output formatting failed: ${error.message}`));
      throw error;
    }
  }

  /**
   * Get agent capabilities
   * @returns {Array} List of capabilities
   */
  getCapabilities() {
    return [
      'get_spreadsheet_details',
      'get_sheet_metadata',
      'get_sheet_data',
      'filter_specific_sheets',
      'calculate_data_dimensions',
      'handle_sheet_errors'
    ];
  }

  /**
   * Get usage examples
   * @returns {Array} List of usage examples
   */
  static getUsageExamples() {
    return [
      {
        description: 'Get basic spreadsheet details',
        input: {
          spreadsheetId: '1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms',
          includeSheetData: false
        },
        expectedOutput: 'Spreadsheet metadata with sheet list but no data'
      },
      {
        description: 'Get complete spreadsheet details with data',
        input: {
          spreadsheetId: '1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms',
          includeSheetData: true,
          includeMetadata: true
        },
        expectedOutput: 'Complete spreadsheet details including all sheet data'
      },
      {
        description: 'Get details for specific sheets only',
        input: {
          spreadsheetId: '1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms',
          includeSheetData: true,
          specificSheets: ['Sheet1', 'Data']
        },
        expectedOutput: 'Details for only the specified sheets'
      }
    ];
  }
}
