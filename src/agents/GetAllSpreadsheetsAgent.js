import { BaseAgent } from './BaseAgent.js';
import { DriveUtilityApp } from '../services/DriveUtilityApp.js';
import { AIMessage } from '@langchain/core/messages';
import { logger } from '../utils/logger.js';

/**
 * Agent to get all available spreadsheets for a user
 * Uses DriveUtilityApp.getAvailableSheets method
 */
export class GetAllSpreadsheetsAgent extends BaseAgent {
  constructor(llmModel = 'gpt-4o-mini') {
    super(
      'GetAllSpreadsheetsAgent',
      'I help users retrieve a list of all their Google Sheets spreadsheets. I can fetch spreadsheet metadata including titles, IDs, and basic information.',
      llmModel
    );
  }

  /**
   * Initialize the agent's state graph
   */
  async initializeGraph() {
    this.graph = this.createBasicWorkflow();
  }

  /**
   * Process input parameters
   * @param {Object} state - Current state
   * @returns {Object} Updated state
   */
  async processInput(state) {
    try {
      state.messages.push(new AIMessage('Processing input for getting all spreadsheets...'));
      
      // For this agent, we don't need specific input parameters
      // We'll get all spreadsheets for the authenticated user
      const input = state.input || {};
      
      // Validate any optional filters
      if (input.limit && (typeof input.limit !== 'number' || input.limit < 1)) {
        throw new Error('Limit must be a positive number');
      }
      
      if (input.offset && (typeof input.offset !== 'number' || input.offset < 0)) {
        throw new Error('Offset must be a non-negative number');
      }

      state.processedInput = {
        limit: input.limit || null,
        offset: input.offset || 0,
        includeHidden: input.includeHidden || false,
        sortBy: input.sortBy || 'title', // title, createdTime, modifiedTime
        sortOrder: input.sortOrder || 'asc' // asc, desc
      };

      state.messages.push(new AIMessage('Input processed successfully'));
      return state;
    } catch (error) {
      state.error = error.message;
      state.messages.push(new AIMessage(`Input processing failed: ${error.message}`));
      throw error;
    }
  }

  /**
   * Execute the main operation - get all spreadsheets
   * @param {Object} state - Current state
   * @returns {Object} Updated state
   */
  async executeOperation(state) {
    try {
      state.messages.push(new AIMessage('Fetching all spreadsheets from Google Drive...'));
      
      // Note: DriveUtilityApp.getAvailableSheets requires a spreadsheetId
      // We need to use a different approach to get all spreadsheets
      // For now, we'll create a method that lists all spreadsheets
      
      // Since DriveUtilityApp doesn't have a method to list all spreadsheets,
      // we'll need to use the Google Drive API directly through GoogleAuthService
      const result = await this.getAllUserSpreadsheets(state.userId, state.processedInput);
      
      state.operationResult = result;
      state.messages.push(new AIMessage(`Successfully retrieved ${result.spreadsheets?.length || 0} spreadsheets`));
      
      return state;
    } catch (error) {
      state.error = error.message;
      state.messages.push(new AIMessage(`Operation failed: ${error.message}`));
      throw error;
    }
  }

  /**
   * Get all user spreadsheets using Google Drive API
   * @param {string} userId - User ID
   * @param {Object} options - Query options
   * @returns {Promise<Object>} List of spreadsheets
   */
  async getAllUserSpreadsheets(userId, options = {}) {
    try {
      // Import GoogleAuthService here to avoid circular dependencies
      const { GoogleAuthService } = await import('../services/GoogleAuthService.js');
      
      // Get authenticated Drive client
      const driveClient = await GoogleAuthService.getApiClient(userId, 'drive');
      
      // Build query parameters
      const queryParams = {
        q: "mimeType='application/vnd.google-apps.spreadsheet'",
        fields: 'nextPageToken, files(id, name, createdTime, modifiedTime, webViewLink, owners, shared, size)',
        orderBy: `${options.sortBy} ${options.sortOrder}`,
        pageSize: Math.min(options.limit || 100, 1000) // Google Drive API max is 1000
      };

      if (options.offset > 0) {
        // For pagination, we'll need to implement token-based pagination
        // This is a simplified approach
        queryParams.pageToken = options.pageToken;
      }

      const response = await driveClient.files.list(queryParams);
      
      const spreadsheets = response.data.files.map(file => ({
        spreadsheetId: file.id,
        title: file.name,
        createdTime: file.createdTime,
        modifiedTime: file.modifiedTime,
        webViewLink: file.webViewLink,
        owners: file.owners,
        shared: file.shared,
        size: file.size
      }));

      return {
        success: true,
        spreadsheets,
        totalCount: spreadsheets.length,
        nextPageToken: response.data.nextPageToken,
        hasMore: !!response.data.nextPageToken
      };
    } catch (error) {
      logger.error('Error fetching user spreadsheets:', error);
      throw new Error(`Failed to fetch spreadsheets: ${error.message}`);
    }
  }

  /**
   * Format the output
   * @param {Object} state - Current state
   * @returns {Object} Updated state
   */
  async formatOutput(state) {
    try {
      if (state.error) {
        state.output = {
          success: false,
          error: state.error,
          timestamp: new Date().toISOString(),
          agentName: this.name
        };
      } else {
        const result = state.operationResult;
        state.output = {
          success: true,
          data: {
            spreadsheets: result.spreadsheets,
            totalCount: result.totalCount,
            hasMore: result.hasMore,
            nextPageToken: result.nextPageToken,
            query: state.processedInput
          },
          timestamp: new Date().toISOString(),
          agentName: this.name,
          metadata: {
            totalSpreadsheets: result.totalCount,
            hasMoreResults: result.hasMore
          }
        };
      }

      state.messages.push(new AIMessage('Output formatted successfully'));
      return state;
    } catch (error) {
      state.error = error.message;
      state.messages.push(new AIMessage(`Output formatting failed: ${error.message}`));
      throw error;
    }
  }

  /**
   * Get agent capabilities
   * @returns {Array} List of capabilities
   */
  getCapabilities() {
    return [
      'list_all_spreadsheets',
      'filter_spreadsheets',
      'sort_spreadsheets',
      'paginate_results',
      'get_spreadsheet_metadata'
    ];
  }

  /**
   * Get usage examples
   * @returns {Array} List of usage examples
   */
  static getUsageExamples() {
    return [
      {
        description: 'Get all spreadsheets',
        input: {},
        expectedOutput: 'List of all user spreadsheets with metadata'
      },
      {
        description: 'Get first 10 spreadsheets sorted by modification date',
        input: {
          limit: 10,
          sortBy: 'modifiedTime',
          sortOrder: 'desc'
        },
        expectedOutput: 'First 10 spreadsheets sorted by most recently modified'
      },
      {
        description: 'Get spreadsheets with pagination',
        input: {
          limit: 20,
          offset: 40
        },
        expectedOutput: 'Spreadsheets 41-60 from the user\'s collection'
      }
    ];
  }
}
