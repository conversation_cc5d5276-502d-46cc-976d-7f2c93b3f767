import { StateGraph, END } from '@langchain/langgraph';
import { HumanMessage, AIMessage, SystemMessage } from '@langchain/core/messages';
import { logger } from '../utils/logger.js';
import { LLMService } from '../services/LLMService.js';
import { DriveUtilityApp } from '../services/DriveUtilityApp.js';

/**
 * Base Agent Class for Google Sheets Operations
 * Provides common functionality and LangGraph integration for all agents
 */
export class BaseAgent {
  constructor(name, description, llmModel = 'gpt-4o-mini') {
    this.name = name;
    this.description = description;
    this.llmModel = llmModel;
    this.graph = null;
    this.state = {
      messages: [],
      userId: null,
      input: null,
      output: null,
      error: null,
      metadata: {}
    };
  }

  /**
   * Initialize the agent's state graph
   * This method should be overridden by child classes
   */
  async initializeGraph() {
    throw new Error('initializeGraph method must be implemented by child classes');
  }

  /**
   * Execute the agent with given input
   * @param {string} userId - User ID for authentication
   * @param {Object} input - Input parameters for the agent
   * @returns {Promise<Object>} Agent execution result
   */
  async execute(userId, input) {
    try {
      logger.info(`Executing agent: ${this.name} for user: ${userId}`);
      
      // Initialize state
      this.state = {
        messages: [
          new SystemMessage(`You are ${this.name}. ${this.description}`)
        ],
        userId,
        input,
        output: null,
        error: null,
        metadata: {
          startTime: new Date().toISOString(),
          agentName: this.name
        }
      };

      // Initialize graph if not already done
      if (!this.graph) {
        await this.initializeGraph();
      }

      // Execute the graph
      const result = await this.graph.invoke(this.state);
      
      // Add completion metadata
      result.metadata.endTime = new Date().toISOString();
      result.metadata.duration = new Date(result.metadata.endTime) - new Date(result.metadata.startTime);

      logger.info(`Agent ${this.name} completed successfully`, {
        userId,
        duration: result.metadata.duration,
        hasOutput: !!result.output
      });

      return {
        success: true,
        agentName: this.name,
        output: result.output,
        metadata: result.metadata,
        error: null
      };

    } catch (error) {
      logger.error(`Agent ${this.name} execution failed:`, error);
      
      return {
        success: false,
        agentName: this.name,
        output: null,
        metadata: {
          ...this.state.metadata,
          endTime: new Date().toISOString(),
          error: error.message
        },
        error: error.message
      };
    }
  }

  /**
   * Validate user authentication and permissions
   * @param {Object} state - Current state
   * @returns {Object} Updated state
   */
  async validateUser(state) {
    try {
      if (!state.userId) {
        throw new Error('User ID is required');
      }

      // Add validation message
      state.messages.push(new AIMessage('Validating user authentication and Google Sheets permissions...'));
      
      // The DriveUtilityApp will handle the actual validation
      // We just need to ensure userId is present
      state.metadata.userValidated = true;
      
      return state;
    } catch (error) {
      state.error = error.message;
      state.messages.push(new AIMessage(`Validation failed: ${error.message}`));
      throw error;
    }
  }

  /**
   * Process input parameters
   * This method should be overridden by child classes
   * @param {Object} state - Current state
   * @returns {Object} Updated state
   */
  async processInput(state) {
    state.messages.push(new AIMessage('Processing input parameters...'));
    return state;
  }

  /**
   * Execute the main operation
   * This method should be overridden by child classes
   * @param {Object} state - Current state
   * @returns {Object} Updated state
   */
  async executeOperation(state) {
    throw new Error('executeOperation method must be implemented by child classes');
  }

  /**
   * Format the output
   * @param {Object} state - Current state
   * @returns {Object} Updated state
   */
  async formatOutput(state) {
    try {
      if (state.error) {
        state.output = {
          success: false,
          error: state.error,
          timestamp: new Date().toISOString()
        };
      } else {
        // Default formatting - child classes can override
        state.output = {
          success: true,
          data: state.operationResult,
          timestamp: new Date().toISOString(),
          agentName: this.name
        };
      }

      state.messages.push(new AIMessage('Output formatted successfully'));
      return state;
    } catch (error) {
      state.error = error.message;
      state.messages.push(new AIMessage(`Output formatting failed: ${error.message}`));
      throw error;
    }
  }

  /**
   * Handle errors in the workflow
   * @param {Object} state - Current state
   * @returns {Object} Updated state
   */
  async handleError(state) {
    logger.error(`Agent ${this.name} error:`, state.error);
    
    state.messages.push(new AIMessage(`Error occurred: ${state.error}`));
    state.output = {
      success: false,
      error: state.error,
      timestamp: new Date().toISOString(),
      agentName: this.name
    };
    
    return state;
  }

  /**
   * Create a basic workflow graph
   * Child classes can override this to create custom workflows
   */
  createBasicWorkflow() {
    const workflow = new StateGraph({
      channels: {
        messages: [],
        userId: null,
        input: null,
        output: null,
        error: null,
        metadata: {},
        operationResult: null
      }
    });

    // Add nodes
    workflow.addNode('validate_user', this.validateUser.bind(this));
    workflow.addNode('process_input', this.processInput.bind(this));
    workflow.addNode('execute_operation', this.executeOperation.bind(this));
    workflow.addNode('format_output', this.formatOutput.bind(this));
    workflow.addNode('handle_error', this.handleError.bind(this));

    // Add edges
    workflow.addEdge('validate_user', 'process_input');
    workflow.addEdge('process_input', 'execute_operation');
    workflow.addEdge('execute_operation', 'format_output');
    workflow.addEdge('format_output', END);
    workflow.addEdge('handle_error', END);

    // Add conditional edges for error handling
    workflow.addConditionalEdges(
      'validate_user',
      (state) => state.error ? 'handle_error' : 'process_input'
    );
    
    workflow.addConditionalEdges(
      'process_input',
      (state) => state.error ? 'handle_error' : 'execute_operation'
    );
    
    workflow.addConditionalEdges(
      'execute_operation',
      (state) => state.error ? 'handle_error' : 'format_output'
    );

    // Set entry point
    workflow.setEntryPoint('validate_user');

    return workflow.compile();
  }

  /**
   * Get agent information
   * @returns {Object} Agent metadata
   */
  getInfo() {
    return {
      name: this.name,
      description: this.description,
      llmModel: this.llmModel,
      capabilities: this.getCapabilities()
    };
  }

  /**
   * Get agent capabilities
   * This method should be overridden by child classes
   * @returns {Array} List of capabilities
   */
  getCapabilities() {
    return ['base_agent_functionality'];
  }
}
