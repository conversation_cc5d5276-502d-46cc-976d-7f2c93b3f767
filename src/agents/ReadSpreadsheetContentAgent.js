import { BaseAgent } from './BaseAgent.js';
import { DriveUtilityApp } from '../services/DriveUtilityApp.js';
import { AIMessage } from '@langchain/core/messages';
import { logger } from '../utils/logger.js';

/**
 * Agent to read content from a Google Sheets spreadsheet
 * Uses DriveUtilityApp.readSpreadsheetContent method
 */
export class ReadSpreadsheetContentAgent extends BaseAgent {
  constructor(llmModel = 'gpt-4o-mini') {
    super(
      'ReadSpreadsheetContentAgent',
      'I help users read content from Google Sheets spreadsheets. I can read entire spreadsheets, specific sheets, or specific ranges of cells.',
      llmModel
    );
  }

  /**
   * Initialize the agent's state graph
   */
  async initializeGraph() {
    this.graph = this.createBasicWorkflow();
  }

  /**
   * Process input parameters
   * @param {Object} state - Current state
   * @returns {Object} Updated state
   */
  async processInput(state) {
    try {
      state.messages.push(new AIMessage('Processing input for reading spreadsheet content...'));
      
      const input = state.input || {};
      
      // Validate required spreadsheet ID
      if (!input.spreadsheetId) {
        throw new Error('Spreadsheet ID is required');
      }
      
      if (typeof input.spreadsheetId !== 'string' || input.spreadsheetId.trim().length === 0) {
        throw new Error('Spreadsheet ID must be a non-empty string');
      }

      // Validate optional parameters
      const sheetName = input.sheetName || null;
      const range = input.range || null;
      const valueRenderOption = input.valueRenderOption || 'FORMATTED_VALUE';
      const dateTimeRenderOption = input.dateTimeRenderOption || 'FORMATTED_STRING';
      
      // Validate value render option
      const validValueRenderOptions = ['FORMATTED_VALUE', 'UNFORMATTED_VALUE', 'FORMULA'];
      if (!validValueRenderOptions.includes(valueRenderOption)) {
        throw new Error(`Invalid valueRenderOption. Must be one of: ${validValueRenderOptions.join(', ')}`);
      }
      
      // Validate date time render option
      const validDateTimeRenderOptions = ['SERIAL_NUMBER', 'FORMATTED_STRING'];
      if (!validDateTimeRenderOptions.includes(dateTimeRenderOption)) {
        throw new Error(`Invalid dateTimeRenderOption. Must be one of: ${validDateTimeRenderOptions.join(', ')}`);
      }

      // Validate range format if provided
      if (range && typeof range !== 'string') {
        throw new Error('Range must be a string (e.g., "A1:C10")');
      }

      // Validate sheet name if provided
      if (sheetName && typeof sheetName !== 'string') {
        throw new Error('Sheet name must be a string');
      }

      state.processedInput = {
        spreadsheetId: input.spreadsheetId.trim(),
        sheetName: sheetName?.trim() || null,
        range: range?.trim() || null,
        valueRenderOption,
        dateTimeRenderOption,
        includeEmpty: input.includeEmpty !== false, // default true
        maxRows: input.maxRows || null,
        maxColumns: input.maxColumns || null
      };

      state.messages.push(new AIMessage('Input processed successfully'));
      return state;
    } catch (error) {
      state.error = error.message;
      state.messages.push(new AIMessage(`Input processing failed: ${error.message}`));
      throw error;
    }
  }

  /**
   * Execute the main operation - read spreadsheet content
   * @param {Object} state - Current state
   * @returns {Object} Updated state
   */
  async executeOperation(state) {
    try {
      const { 
        spreadsheetId, 
        sheetName, 
        range, 
        valueRenderOption, 
        dateTimeRenderOption,
        maxRows,
        maxColumns
      } = state.processedInput;
      
      state.messages.push(new AIMessage(`Reading content from spreadsheet: ${spreadsheetId}`));
      
      // Prepare options for DriveUtilityApp
      const options = {
        valueRenderOption,
        dateTimeRenderOption
      };
      
      // Add sheet name if specified
      if (sheetName) {
        options.sheetName = sheetName;
        state.messages.push(new AIMessage(`Targeting specific sheet: ${sheetName}`));
      }
      
      // Add range if specified
      if (range) {
        options.range = range;
        state.messages.push(new AIMessage(`Targeting specific range: ${range}`));
      }
      
      // Read the spreadsheet content
      const result = await DriveUtilityApp.readSpreadsheetContent(
        state.userId, 
        spreadsheetId, 
        options
      );
      
      if (!result.success) {
        throw new Error('Failed to read spreadsheet content');
      }

      // Apply row and column limits if specified
      if (maxRows || maxColumns) {
        result.sheetData = result.sheetData.map(sheet => ({
          ...sheet,
          values: this.applyDataLimits(sheet.values, maxRows, maxColumns)
        }));
      }

      // Add summary information
      const summary = this.generateContentSummary(result);
      
      state.operationResult = {
        ...result,
        summary,
        readOptions: state.processedInput
      };
      
      state.messages.push(new AIMessage(`Successfully read content: ${summary.totalCells} cells from ${summary.totalSheets} sheet(s)`));
      
      return state;
    } catch (error) {
      state.error = error.message;
      state.messages.push(new AIMessage(`Operation failed: ${error.message}`));
      throw error;
    }
  }

  /**
   * Apply row and column limits to data
   * @param {Array} values - 2D array of values
   * @param {number} maxRows - Maximum number of rows
   * @param {number} maxColumns - Maximum number of columns
   * @returns {Array} Limited data
   */
  applyDataLimits(values, maxRows, maxColumns) {
    if (!values || !Array.isArray(values)) return values;
    
    let limitedValues = values;
    
    // Apply row limit
    if (maxRows && maxRows > 0) {
      limitedValues = limitedValues.slice(0, maxRows);
    }
    
    // Apply column limit
    if (maxColumns && maxColumns > 0) {
      limitedValues = limitedValues.map(row => 
        Array.isArray(row) ? row.slice(0, maxColumns) : row
      );
    }
    
    return limitedValues;
  }

  /**
   * Generate content summary
   * @param {Object} result - Read result
   * @returns {Object} Summary information
   */
  generateContentSummary(result) {
    const summary = {
      totalSheets: result.sheetData?.length || 0,
      totalRows: 0,
      totalColumns: 0,
      totalCells: 0,
      nonEmptyCells: 0,
      sheets: []
    };

    if (result.sheetData) {
      result.sheetData.forEach(sheet => {
        const values = sheet.values || [];
        const rowCount = values.length;
        const columnCount = rowCount > 0 ? Math.max(...values.map(row => 
          Array.isArray(row) ? row.length : 0)) : 0;
        const cellCount = rowCount * columnCount;
        const nonEmptyCount = values.reduce((count, row) => 
          count + (Array.isArray(row) ? row.filter(cell => 
            cell !== null && cell !== undefined && cell !== '').length : 0), 0);

        summary.totalRows += rowCount;
        summary.totalColumns = Math.max(summary.totalColumns, columnCount);
        summary.totalCells += cellCount;
        summary.nonEmptyCells += nonEmptyCount;
        
        summary.sheets.push({
          sheetName: sheet.sheetName,
          rowCount,
          columnCount,
          cellCount,
          nonEmptyCount,
          hasData: nonEmptyCount > 0
        });
      });
    }

    return summary;
  }

  /**
   * Format the output
   * @param {Object} state - Current state
   * @returns {Object} Updated state
   */
  async formatOutput(state) {
    try {
      if (state.error) {
        state.output = {
          success: false,
          error: state.error,
          timestamp: new Date().toISOString(),
          agentName: this.name
        };
      } else {
        const result = state.operationResult;
        state.output = {
          success: true,
          data: {
            spreadsheetId: result.spreadsheetId,
            title: result.title,
            content: result.sheetData,
            summary: result.summary,
            query: {
              spreadsheetId: state.processedInput.spreadsheetId,
              sheetName: state.processedInput.sheetName,
              range: state.processedInput.range,
              valueRenderOption: state.processedInput.valueRenderOption,
              dateTimeRenderOption: state.processedInput.dateTimeRenderOption
            }
          },
          timestamp: new Date().toISOString(),
          agentName: this.name,
          metadata: {
            totalSheets: result.summary.totalSheets,
            totalCells: result.summary.totalCells,
            nonEmptyCells: result.summary.nonEmptyCells
          }
        };
      }

      state.messages.push(new AIMessage('Output formatted successfully'));
      return state;
    } catch (error) {
      state.error = error.message;
      state.messages.push(new AIMessage(`Output formatting failed: ${error.message}`));
      throw error;
    }
  }

  /**
   * Get agent capabilities
   * @returns {Array} List of capabilities
   */
  getCapabilities() {
    return [
      'read_entire_spreadsheet',
      'read_specific_sheet',
      'read_specific_range',
      'format_value_rendering',
      'handle_date_formatting',
      'apply_data_limits',
      'generate_content_summary'
    ];
  }

  /**
   * Get usage examples
   * @returns {Array} List of usage examples
   */
  static getUsageExamples() {
    return [
      {
        description: 'Read entire spreadsheet',
        input: {
          spreadsheetId: '1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms'
        },
        expectedOutput: 'All content from all sheets in the spreadsheet'
      },
      {
        description: 'Read specific sheet',
        input: {
          spreadsheetId: '1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms',
          sheetName: 'Sheet1'
        },
        expectedOutput: 'All content from Sheet1 only'
      },
      {
        description: 'Read specific range',
        input: {
          spreadsheetId: '1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms',
          sheetName: 'Sheet1',
          range: 'A1:C10'
        },
        expectedOutput: 'Content from cells A1 to C10 in Sheet1'
      },
      {
        description: 'Read with formulas',
        input: {
          spreadsheetId: '1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms',
          valueRenderOption: 'FORMULA'
        },
        expectedOutput: 'Raw formulas instead of calculated values'
      }
    ];
  }
}
